<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Sunflower petals -->
  <g transform="translate(100,80)">
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(0)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(30)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(60)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(90)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(120)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(150)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(180)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(210)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(240)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(270)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(300)"/>
    <ellipse rx="8" ry="20" fill="#FFD700" transform="rotate(330)"/>
  </g>
  
  <!-- Center -->
  <circle cx="100" cy="80" r="15" fill="#8B4513"/>
  <circle cx="100" cy="80" r="10" fill="#654321"/>
  
  <!-- Stem -->
  <rect x="95" y="110" width="10" height="60" fill="#228B22"/>
  
  <!-- Leaves -->
  <ellipse cx="80" cy="130" rx="12" ry="20" fill="#32CD32" transform="rotate(-30 80 130)"/>
  <ellipse cx="120" cy="140" rx="12" ry="20" fill="#32CD32" transform="rotate(30 120 140)"/>
</svg>
