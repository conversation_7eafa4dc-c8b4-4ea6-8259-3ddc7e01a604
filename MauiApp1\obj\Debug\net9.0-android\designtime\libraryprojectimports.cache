<Paths><Jars><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\97\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\98\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\99\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\100\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\101\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\102\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\104\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\105\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\106\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\107\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\108\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\109\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\110\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\111\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\112\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\113\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\115\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\116\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\117\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\118\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\119\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\120\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\121\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\122\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\123\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\124\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\125\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\126\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\127\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\128\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\129\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\130\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\130\jl\libs\repackaged.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\131\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\132\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\133\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\134\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\135\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\136\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\137\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\138\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\139\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\140\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\141\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\142\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\143\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\144\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\145\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\146\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\147\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\149\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\150\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\151\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\152\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\153\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\154\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\155\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\156\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\157\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\158\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\159\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\160\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\161\jl\libs\ED64959F88B22E6D.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\162\jl\libs\2E7FD15AFA9B216B.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\164\jl\classes.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\167\jl\libs\F975D0960055A5E3.jar</Jar><Jar>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\168\jl\libs\B71CFF5D5A0B3AEB.jar</Jar></Jars><ResolvedResourceDirectories><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.versionedparcelable\*******\aar\androidx.versionedparcelable.versionedparcelable.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\97\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\97\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.tracing.tracing\*******\aar\androidx.tracing.tracing.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\98\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\98\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.startup.startupruntime\********\aar\androidx.startup.startup-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\99\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\99\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.profileinstaller.profileinstaller\********\aar\androidx.profileinstaller.profileinstaller.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\100\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\100\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.arch.core.runtime\********\aar\androidx.arch.core.core-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\101\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\101\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.runtime.android\*******\aar\androidx.lifecycle.lifecycle-runtime-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\102\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\102\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.interpolator\********\aar\androidx.interpolator.interpolator.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\104\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\104\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.annotation.experimental\*******\aar\androidx.annotation.annotation-experimental.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\105\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\105\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.core\********\aar\androidx.core.core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\106\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\106\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.customview\********\aar\androidx.customview.customview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\107\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\107\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.viewpager\********\aar\androidx.viewpager.viewpager.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\108\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\108\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.savedstate\********\aar\androidx.savedstate.savedstate.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\109\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\109\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.core.core.ktx\********\aar\androidx.core.core-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\110\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\110\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.customview.poolingcontainer\********\aar\androidx.customview.customview-poolingcontainer.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\111\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\111\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.recyclerview\*******\aar\androidx.recyclerview.recyclerview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\112\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\112\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.viewmodel.android\*******\aar\androidx.lifecycle.lifecycle-viewmodel-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\113\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\113\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.livedata.core\*******\aar\androidx.lifecycle.lifecycle-livedata-core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\115\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\115\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.loader\********\aar\androidx.loader.loader.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\116\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\116\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.viewmodelsavedstate\*******\aar\androidx.lifecycle.lifecycle-viewmodel-savedstate.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\117\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\117\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.activity\*******\aar\androidx.activity.activity.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\118\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\118\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.fragment\*******\aar\androidx.fragment.fragment.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\119\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\119\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.viewpager2\*******\aar\androidx.viewpager2.viewpager2.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\120\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\120\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.vectordrawable\*******\aar\androidx.vectordrawable.vectordrawable.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\121\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\121\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.vectordrawable.animated\*******\aar\androidx.vectordrawable.vectordrawable-animated.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\122\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\122\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.print\********\aar\androidx.print.print.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\123\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\123\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.localbroadcastmanager\********\aar\androidx.localbroadcastmanager.localbroadcastmanager.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\124\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\124\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.documentfile\********\aar\androidx.documentfile.documentfile.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\125\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\125\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.legacy.support.core.utils\********\aar\androidx.legacy.legacy-support-core-utils.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\126\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\126\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.dynamicanimation\********\aar\androidx.dynamicanimation.dynamicanimation.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\127\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\127\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.transition\*******\aar\androidx.transition.transition.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\128\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\128\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.process\*******\aar\androidx.lifecycle.lifecycle-process.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\129\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\129\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.emoji2\*******\aar\androidx.emoji2.emoji2.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\130\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\130\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.emoji2.viewshelper\*******\aar\androidx.emoji2.emoji2-views-helper.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\131\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\131\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.drawerlayout\********\aar\androidx.drawerlayout.drawerlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\132\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\132\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.cursoradapter\********\aar\androidx.cursoradapter.cursoradapter.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\133\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\133\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.coordinatorlayout\********\aar\androidx.coordinatorlayout.coordinatorlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\134\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\134\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.appcompat.appcompatresources\*******\aar\androidx.appcompat.appcompat-resources.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\135\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\135\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.appcompat\*******\aar\androidx.appcompat.appcompat.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\136\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\136\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.constraintlayout\********\aar\androidx.constraintlayout.constraintlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\137\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\137\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.cardview\********\aar\androidx.cardview.cardview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\138\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\138\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.google.android.material\********\aar\com.google.android.material.material.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\139\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\139\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.window.extensions.core.core\********\aar\androidx.window.extensions.core.core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\140\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\140\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.window\*******\aar\androidx.window.window.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\141\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\141\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.swiperefreshlayout\********\aar\androidx.swiperefreshlayout.swiperefreshlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\142\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\142\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.slidingpanelayout\********\aar\androidx.slidingpanelayout.slidingpanelayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\143\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\143\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.security.securitycrypto\*******-alpha06\aar\androidx.security.security-crypto.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\144\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\144\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.savedstate.savedstate.ktx\********\aar\androidx.savedstate.savedstate-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\145\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\145\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.viewmodel.ktx\*******\aar\androidx.lifecycle.lifecycle-viewmodel-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\146\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\146\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.runtime.ktx.android\*******\aar\androidx.lifecycle.lifecycle-runtime-ktx-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\147\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\147\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.common\*******\aar\androidx.navigation.navigation-common.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\149\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\149\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.activity.ktx\*******\aar\androidx.activity.activity-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\150\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\150\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.runtime\*******\aar\androidx.navigation.navigation-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\151\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\151\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.ui\*******\aar\androidx.navigation.navigation-ui.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\152\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\152\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.livedata.core.ktx\*******\aar\androidx.lifecycle.lifecycle-livedata-core-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\153\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\153\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.fragment.ktx\*******\aar\androidx.fragment.fragment-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\154\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\154\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.fragment\*******\aar\androidx.navigation.navigation-fragment.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\155\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\155\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.lifecycle.livedata\*******\aar\androidx.lifecycle.lifecycle-livedata.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\156\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\156\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.exifinterface\*******\aar\androidx.exifinterface.exifinterface.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\157\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\157\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.browser\*******\aar\androidx.browser.browser.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\158\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\158\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.android.glide.gifdecoder\********\aar\gifdecoder.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\159\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\159\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.android.glide\********\aar\glide.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\160\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\160\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.14\lib\net9.0-android35.0\Microsoft.Maui.Controls.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\163\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\163\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.14\lib\net9.0-android35.0\maui.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\164\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\164\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.14\lib\net9.0-android35.0\Microsoft.Maui.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\165\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\165\jl\res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="C:\Users\<USER>\.nuget\packages\microsoft.maui.essentials\9.0.14\lib\net9.0-android35.0\Microsoft.Maui.Essentials.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\166\jl\res.zip">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\166\jl\res</ResolvedResourceDirectory></ResolvedResourceDirectories><ResolvedAssetDirectories /><ResolvedEnvironmentFiles /><ResolvedResourceDirectoryStamps><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\97.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\98.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\99.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\100.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\101.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\102.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\104.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\105.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\106.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\107.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\108.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\109.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\110.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\111.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\112.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\113.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\115.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\116.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\117.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\118.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\119.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\120.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\121.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\122.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\123.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\124.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\125.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\126.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\127.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\128.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\129.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\130.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\131.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\132.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\133.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\134.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\135.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\136.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\137.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\138.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\139.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\140.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\141.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\142.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\143.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\144.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\145.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\146.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\147.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\149.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\150.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\151.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\152.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\153.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\154.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\155.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\156.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\157.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\158.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\159.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\160.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\163.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\164.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\165.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\166.stamp</ResolvedResourceDirectoryStamp></ResolvedResourceDirectoryStamps><ProguardConfigFiles><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.coordinatorlayout\********\aar\androidx.coordinatorlayout.coordinatorlayout.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\134\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.appcompat\*******\aar\androidx.appcompat.appcompat.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\136\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.window.extensions.core.core\********\aar\androidx.window.extensions.core.core.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\140\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.window\*******\aar\androidx.window.window.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\141\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.common\*******\aar\androidx.navigation.navigation-common.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\149\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.androidx.navigation.ui\*******\aar\androidx.navigation.navigation-ui.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\152\jl\proguard.txt</ProguardConfigFile><ProguardConfigFile OriginalFile="C:\Users\<USER>\.nuget\packages\xamarin.android.glide\********\aar\glide.aar">C:\Users\<USER>\Desktop\MauiApp1\MauiApp1\obj\Debug\net9.0-android\lp\160\jl\proguard.txt</ProguardConfigFile></ProguardConfigFiles></Paths>