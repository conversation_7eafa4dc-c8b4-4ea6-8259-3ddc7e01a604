<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Daisy petals -->
  <g transform="translate(100,80)">
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(0)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(25)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(50)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(75)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(100)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(125)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(150)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(175)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(200)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(225)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(250)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(275)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(300)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(325)"/>
    <ellipse rx="6" ry="18" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1" transform="rotate(350)"/>
  </g>
  
  <!-- Center -->
  <circle cx="100" cy="80" r="12" fill="#FFD700"/>
  
  <!-- Stem -->
  <rect x="95" y="105" width="10" height="65" fill="#228B22"/>
  
  <!-- Leaves -->
  <ellipse cx="85" cy="135" rx="10" ry="15" fill="#32CD32" transform="rotate(-20 85 135)"/>
  <ellipse cx="115" cy="145" rx="10" ry="15" fill="#32CD32" transform="rotate(20 115 145)"/>
</svg>
